import React, { useState, useRef, useEffect } from 'react'; // Import React and React Hooks
import './acb.css'; // Import component-specific styles

// Ant Design components
import {
    Al<PERSON>,
    Button,
    Drawer,
    Input,
    Popconfirm,
    Popover,
    Tooltip,
    message,
} from 'antd';

// Ant Design icons
import {
    UserOutlined,
    PhoneOutlined,
    MailOutlined,
    EnvironmentOutlined,
    GiftOutlined,
} from '@ant-design/icons';

// React icons
import { FiSend } from 'react-icons/fi';
import { SlArrowDownCircle } from 'react-icons/sl';
import { HiMiniPlus } from 'react-icons/hi2';
import {
    GoThumbsup,
    GoThumbsdown,
    GoSync,
    GoComment,
    GoCommentDiscussion,
    GoHistory,
} from 'react-icons/go';
import { GiBackwardTime } from 'react-icons/gi';
import { SlClose } from 'react-icons/sl';
import { GrNewWindow } from 'react-icons/gr';
import { FiEdit } from 'react-icons/fi';
import { MdOutlineClose } from 'react-icons/md';

// Redux action and helper
import { isMobileView } from '../../../util/helpers';
import { PiNotePencil } from 'react-icons/pi';

// Dummy components rendered based on actions
const CreateServiceRequest = () => <div>Create Service Request</div>;
const AppointmentsToday = () => <div>Appointments Today</div>;
const AssignTask = () => <div>Assign Task to Field Agent</div>;
const FindCustomer = () => <div>Find Customer</div>;

// Static chat history entries
const chatHistoryItems = [
    {
        id: 1,
        title: 'Create ticket for printer issue Create ticket for printer issue Create ticket for printer issue',
        timestamp: '03 Jan 2025',
    },
    {
        id: 2,
        title: 'Assign technician to TMS-987123',
        timestamp: '07 Jan 2025',
    },
    {
        id: 3,
        title: 'Get data for ticket TMS-UAT-987123321AB',
        timestamp: '12 Feb 2025',
    },
    { id: 4, title: "Show today's appointments", timestamp: '18 Feb 2025' },
    {
        id: 5,
        title: 'Find customer by phone: 9876543210',
        timestamp: '02 Mar 2025',
    },
    { id: 6, title: 'Escalate ticket TMS-555882', timestamp: '11 Mar 2025' },
    {
        id: 7,
        title: 'Create service request for AC issue',
        timestamp: '05 Apr 2025',
    },
    {
        id: 8,
        title: 'Update ticket TMS-998877 status to Closed',
        timestamp: '15 Apr 2025',
    },
    { id: 9, title: 'List open tickets in Mumbai', timestamp: '06 May 2025' },
    {
        id: 10,
        title: 'Assign urgent task to technician Rahul',
        timestamp: '14 May 2025',
    },
    {
        id: 11,
        title: 'Reassign ticket TMS-432123 to new agent',
        timestamp: '03 Jun 2025',
    },
    {
        id: 12,
        title: 'Search for completed jobs in May',
        timestamp: '09 Jun 2025',
    },
    {
        id: 13,
        title: 'Customer feedback for TMS-20231108AB',
        timestamp: '01 Jul 2025',
    },
    {
        id: 14,
        title: 'Generate report for site visits',
        timestamp: '10 Jul 2025',
    },
    {
        id: 15,
        title: 'Create bulk tickets for network downtime',
        timestamp: '04 Aug 2025',
    },
];

// Main chat area component
const AiChatBot = ({ aiChatAreaToggle }) => {
    // State variables
    const [selectedComponent, setSelectedComponent] = useState(null);
    const [messages, setMessages] = useState([]);
    const [input, setInput] = useState('');
    const [isScrolledUp, setIsScrolledUp] = useState(false);
    const [lastMessageFrom, setLastMessageFrom] = useState(null);
    const [showHistory, setShowHistory] = useState(false);
    const [attachmentOpen, setAttachmentOpen] = useState(false);
    const [openAiChatDrawer, setOpenAiChatDrawer] = useState(false);
    const [helpful, setHelpful] = useState(false);
    const [helpfulMap, setHelpfulMap] = useState([]);
    const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

    const showDrawer = () => {
        setOpenAiChatDrawer(true);
    };

    const onClose = () => {
        setOpenAiChatDrawer(!openAiChatDrawer);
    };

    const userName = 'Jainish'; // Static user name

    // Refs for scroll handling
    const finalLocationOfMessage = useRef(null);
    const messagesContainerRef = useRef(null);

    // Hide attachments popover
    const hideAttachment = () => {
        setAttachmentOpen(false);
    };

    // Open/close attachments popover
    const handleAttachmentBubble = (newOpen) => {
        setAttachmentOpen(newOpen);
    };

    // Scroll to bottom of message list
    const scrollToBottom = () => {
        if (finalLocationOfMessage.current) {
            finalLocationOfMessage.current.scrollIntoView({
                behavior: 'smooth',
            });
        }
    };

    const FeedbackSection = ({ idx, msg }) => {
        const wrapperRef = useRef(null);

        useEffect(() => {
            function handleClickOutside(event) {
                if (
                    wrapperRef.current &&
                    !wrapperRef.current.contains(event.target)
                ) {
                    toggleFeedbackSection(idx); // Close feedback section
                }
            }

            document.addEventListener('click', handleClickOutside); // safer than mousedown
            return () => {
                document.removeEventListener('click', handleClickOutside);
            };
        }, [idx]);

        return (
            msg.showFeedbackOptions && (
                <div
                    ref={wrapperRef}
                    className="wy-acb-feedback-section-wrapper"
                >
                    <div className="wy-acb-feedback-section-header gx-mb-3 gx-position-relative">
                        <div className="wy-acb-title-decor"></div>
                        <div>Tell us more...</div>
                        <MdOutlineClose
                            className="gx-text-red wy-cursor-pointer gx-fs-xl"
                            onClick={() => toggleFeedbackSection(idx)}
                        />
                    </div>
                    <div>
                        <div>
                            <div className="wy-acb-feedback-section">
                                <Button
                                    block
                                    type={
                                        msg.feedbackType?.includes('confused')
                                            ? 'primary'
                                            : 'ghost'
                                    }
                                    onClick={() =>
                                        handleFeedbackClick(idx, 'confused')
                                    }
                                    className="gx-mr-0"
                                >
                                    Didn't fully follow instructions
                                </Button>

                                <Button
                                    block
                                    type={
                                        msg.feedbackType?.includes('irrelevant')
                                            ? 'primary'
                                            : 'ghost'
                                    }
                                    onClick={() =>
                                        handleFeedbackClick(idx, 'irrelevant')
                                    }
                                    className="gx-mr-0"
                                >
                                    Inaccurate result
                                </Button>

                                <Button
                                    block
                                    type={
                                        msg.feedbackType?.includes(
                                            'not_relevant_2'
                                        )
                                            ? 'primary'
                                            : 'ghost'
                                    }
                                    onClick={() =>
                                        handleFeedbackClick(
                                            idx,
                                            'not_relevant_2'
                                        )
                                    }
                                    className="gx-mr-0"
                                >
                                    This does not make sense
                                </Button>
                            </div>
                            <div className="gx-mb-2">
                                <Input.TextArea
                                    value={msg.customFeedback}
                                    placeholder="Please describe what could be improved... (optional)"
                                    rows={2}
                                    className="wy-acb-textarea"
                                    onChange={(e) =>
                                        handleCustomFeedbackChange(
                                            idx,
                                            e.target.value
                                        )
                                    }
                                />
                            </div>
                            <div className="gx-text-right gx-mt-1">
                                <Button
                                    danger
                                    type="text"
                                    onClick={() => toggleFeedbackSection(idx)}
                                    className="gx-mr-2 gx-mt-1 gx-mb-0"
                                >
                                    Cancel
                                </Button>
                                <Button
                                    type="primary"
                                    onClick={() =>
                                        handleCustomFeedbackSubmit(idx)
                                    }
                                    className="gx-mt-1 gx-mb-0"
                                >
                                    Submit
                                </Button>
                            </div>
                        </div>
                    </div>
                </div>
            )
        );
    };
    // Confirm delete history
    const handleDeleteHistoryConfirm = (e) => {
        message.success('History Deleted');
    };

    const handleDeleteHistoryCancel = (e) => {
        console.log(e);
    };

    const handleThumbsUpHelpfulClick = (idx) => {
        setHelpfulMap((prev) => ({
            ...prev,
            [idx]: !prev[idx],
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === idx ? { ...msg, showFeedbackOptions: false } : msg
            )
        );
    };

    const toggleFeedbackSection = (idx) => {
        setHelpfulMap((prev) => ({
            ...prev,
            [idx]: false,
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === idx
                    ? { ...msg, showFeedbackOptions: !msg.showFeedbackOptions }
                    : msg
            )
        );
    };

    const handleFeedbackClick = (idx, selectedType) => {
        setMessages((prevMessages) =>
            prevMessages.map((msg, i) => {
                if (i !== idx) return msg;

                const currentTypes = Array.isArray(msg.feedbackType)
                    ? msg.feedbackType
                    : [];
                const alreadySelected = currentTypes.includes(selectedType);

                const updatedTypes = alreadySelected
                    ? currentTypes.filter((type) => type !== selectedType) // deselect
                    : [...currentTypes, selectedType]; // select

                return {
                    ...msg,
                    feedbackType: updatedTypes,
                    showFeedbackOptions: true,
                    customFeedback: '',
                };
            })
        );
    };

    const handleCustomFeedbackChange = (index, value) => {
        setMessages((prevMessages) =>
            prevMessages.map((msg, i) =>
                i === index ? { ...msg, customFeedback: value } : msg
            )
        );
    };

    const handleCustomFeedbackSubmit = (index) => {
        setFeedbackSubmitted(true);

        setHelpfulMap((prev) => ({
            ...prev,
            [index]: false,
        }));

        setMessages((prev) =>
            prev.map((msg, i) =>
                i === index
                    ? {
                          ...msg,
                          showFeedbackOptions: false,
                          feedbackType: null,
                          customFeedback: '',
                          feedbackSubmitted: true,
                      }
                    : msg
            )
        );
    };

    const getCurrentTime = () =>
        new Date().toLocaleTimeString([], {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        });

    useEffect(() => {
        if (lastMessageFrom === 'user') {
            const timeout = setTimeout(() => {
                scrollToBottom();
            }, 100);
            return () => clearTimeout(timeout);
        }
    }, [messages, lastMessageFrom]);

    useEffect(() => {
        const container = messagesContainerRef.current;
        const handleScroll = () => {
            const nearBottom =
                container.scrollHeight - container.scrollTop <=
                container.clientHeight + 100;
            setIsScrolledUp(!nearBottom);
        };

        if (container) {
            container.addEventListener('scroll', handleScroll);
        }
        return () => {
            if (container) {
                container.removeEventListener('scroll', handleScroll);
            }
        };
    }, []);

    const handleSendButton = () => {
        if (!input.trim()) return;
        const newMsg = {
            from: 'user',
            text: input,
            time: getCurrentTime(),
        };
        setMessages((prev) => [...prev, newMsg]);
        setLastMessageFrom('user');
        setInput('');
        scrollToBottom();
    };

    const handleActionClick = (actionText) => {
        const time = getCurrentTime();
        const userMessage = {
            from: 'user',
            text: actionText,
            time,
        };
        const waitingMessage = {
            from: 'tms-ai',
            text: <div className="wy-acb-initial-loading"></div>,
            time,
            isWaiting: true,
        };
        setMessages((prev) => [...prev, userMessage, waitingMessage]);
        setLastMessageFrom('tms-ai');

        setTimeout(() => {
            setMessages((prev) =>
                prev.map((msg) =>
                    msg.isWaiting
                        ? { ...msg, text: `${actionText}`, isWaiting: false }
                        : msg
                )
            );

            switch (actionText) {
                case 'Show all appointments today':
                    setSelectedComponent(<AppointmentsToday />);
                    break;
                case 'Find customer by phone number':
                    setSelectedComponent(<FindCustomer />);
                    break;
                case 'Create service request':
                    setSelectedComponent(<CreateServiceRequest />);
                    break;
                case 'Assign task to field agent':
                    setSelectedComponent(<AssignTask />);
                    break;
                default:
                    setSelectedComponent(null);
            }
        }, 3000);
    };

    const renderHistoryView = () => (
        <div className="wy-acb-history-wrapper">
            <Button type="primary" block onClick={() => setShowHistory(false)}>
                New Chat
            </Button>
            <p className="gx-text-grey gx-fs-sm gx-mb-2 gx-mt-4">History</p>
            <div className="wy-acb-chat-history-list">
                {chatHistoryItems.map((item) => (
                    <Button
                        key={item.id}
                        type="ghost"
                        block
                        className="gx-mb-2 gx-text-left gx-border-0 gx-mr-0 wy-acb-history-switch-button"
                    >
                        <div className="gx-d-flex gx-align-items-center gx-justify-content-between">
                            <div className="wy-acb-history-title">
                                <GiBackwardTime className="gx-mr-2" />
                                {item.title}
                            </div>
                            <p className="gx-text-grey gx-fs-sm gx-mb-0">
                                {item.timestamp}
                            </p>
                        </div>
                    </Button>
                ))}
            </div>
            <Popconfirm
                title="You will lose all your history?"
                onConfirm={handleDeleteHistoryConfirm}
                onCancel={handleDeleteHistoryCancel}
                okText="Yes"
                cancelText="No"
            >
                <Button className="gx-mt-2" block danger type="dashed">
                    Delete History
                </Button>
            </Popconfirm>
        </div>
    );

    const renderChatView = () => (
        <>
            <div className="wy-acb-messages" ref={messagesContainerRef}>
                {messages.map((msg, idx) => (
                    <div
                        key={idx}
                        className={`wy-acb-message-wrapper ${msg.from === 'user' ? 'user' : 'ai'}`}
                    >
                        <div className="message-wrapper">
                            <div className="wy-acb-message-bubble">
                                {msg.text}
                            </div>
                            {!msg.isWaiting && (
                                <div className="gx-d-flex gx-align-items-center gx-justify-content-between gx-mt-1">
                                    <div className="wy-acb-feedback-bubble-wrapper">
                                        <div className="wy-acb-feedback-bubble">
                                            <button
                                                className={`wy-acb-feedback-btn helpful ${
                                                    helpfulMap[idx]
                                                        ? 'wy-acb-feedback-helpful-btn-active'
                                                        : ''
                                                } ${msg.showFeedbackOptions || msg.feedbackSubmitted ? 'gx-d-none' : ''}`}
                                                title="Helpful"
                                                onClick={() =>
                                                    handleThumbsUpHelpfulClick(
                                                        idx
                                                    )
                                                }
                                            >
                                                <GoThumbsup
                                                    stroke={
                                                        helpfulMap[idx]
                                                            ? 'green'
                                                            : undefined
                                                    }
                                                    strokeWidth={
                                                        helpfulMap[idx] ? 1 : 0
                                                    }
                                                />
                                            </button>

                                            <button
                                                className={`wy-acb-feedback-btn not-helpful 
                                                            ${msg.feedbackSubmitted || msg.showFeedbackOptions ? 'wy-acb-feedback-not-helpful-btn-active' : ''} 
                                                            ${helpfulMap[idx] ? 'gx-d-none' : ''}`}
                                                title="Not Helpful"
                                                onClick={() =>
                                                    toggleFeedbackSection(idx)
                                                }
                                            >
                                                <GoThumbsdown
                                                    color={
                                                        msg.showFeedbackOptions
                                                            ? 'black'
                                                            : undefined
                                                    }
                                                />
                                            </button>
                                        </div>
                                        {msg.feedbackSubmitted && (
                                            <Alert
                                                showIcon
                                                message="Thank you for your feedback!"
                                                type="success"
                                                className="gx-mt-2 wy-acb-full-width-chat-area gx-fs-sm"
                                            />
                                        )}
                                    </div>
                                    <div className="wy-acb-timestamp gx-d-none">
                                        {msg.time}
                                    </div>
                                </div>
                            )}
                            {msg.showFeedbackOptions && (
                                <FeedbackSection idx={idx} msg={msg} />
                            )}
                        </div>
                    </div>
                ))}
                <div ref={finalLocationOfMessage} />
            </div>

            {isScrolledUp && (
                <div>
                    <button
                        onClick={scrollToBottom}
                        className="wy-acb-jump-to-latest-button"
                    >
                        <SlArrowDownCircle /> Jump to Latest
                    </button>
                </div>
            )}

            {messages.length === 0 && (
                <div className="wy-acb-intro-message gx-text-center">
                    <div className="wy-acb-name">Hello! Jainish Zobalia</div> I
                    am your TMS AI Assistant.
                    <br />
                    How can I help you with your TMS operations today?
                </div>
            )}

            <div className="wy-acb-action-buttons">
                {[
                    'Show all appointments today',
                    'Find customer by phone number',
                    'Create service request',
                    'Assign task to field agent',
                ].map((btn, i) => (
                    <button
                        key={i}
                        className="wy-acb-action-button"
                        onClick={() => handleActionClick(btn)}
                    >
                        {btn}
                    </button>
                ))}
            </div>

            <div className="wy-acb-prompt-wrapper">
                <textarea
                    placeholder="Type a message..."
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter' && !e.shiftKey) {
                            e.preventDefault(); // ✅ Prevents new line
                            handleSendButton();
                        }
                    }}
                    className="wy-acb-prompt-textarea"
                />
                <button
                    onClick={handleSendButton}
                    className="wy-acb-send-button"
                >
                    <FiSend />
                </button>
            </div>
        </>
    );

    // Component UI rendered as Drawer
    return (
        <>
            <Button type="primary" onClick={showDrawer}>
                Open AI Chat Bot
            </Button>
            <Drawer
                placement={isMobileView() ? 'bottom' : 'right'}
                visible={openAiChatDrawer}
                onClose={onClose}
                className="wy-acb-drawer-wrapper"
                headerStyle={{ display: 'none' }}
            >
                {/* Main container */}
                <div className="wy-acb-chat-container box">
                    {/* Header with controls */}
                    <div className="wy-acb-chat-header gx-d-flex gx-justify-content-between gx-align-items-center">
                        <Tooltip
                            title={
                                showHistory ? 'Back to Chat' : 'View History'
                            }
                            placement={isMobileView() ? 'left' : 'bottom'}
                        >
                            <Button
                                size="small"
                                onClick={() => setShowHistory((prev) => !prev)}
                                className="gx-mb-0 gx-d-flex gx-align-items-center gx-fs-18 wy-acb-history-button gx-mr-0 gx-d-none"
                            >
                                {showHistory ? (
                                    <GoCommentDiscussion />
                                ) : (
                                    <GoHistory />
                                )}
                            </Button>
                        </Tooltip>
                        <h2 className="gx-mb-0 wy-acb-title-ai-assistant">
                            TMS AI Assistant
                        </h2>
                        <div className="gx-d-flex gx-align-items-center">
                            <Tooltip title="Start New Chat" placement="bottom">
                                <Button
                                    size="small"
                                    className="gx-mr-0 gx-mb-0 wy-acb-new-chat-button gx-fs-18 gx-d-flex gx-align-items-center"
                                >
                                    <PiNotePencil />
                                </Button>
                            </Tooltip>
                            <button
                                onClick={() => onClose()}
                                className="gx-bg-transparent gx-border-0 gx-text-red gx-fs-md wy-cursor-pointer main-interface-close-button gx-d-none"
                            >
                                <SlClose />
                            </button>
                        </div>
                    </div>

                    {/* Conditional views: history or chat */}
                    {showHistory ? renderHistoryView() : renderChatView()}
                </div>
            </Drawer>
        </>
    );
};

export default AiChatBot;
