{"compilerOptions": {"target": "ES6", "module": "ESNext", "jsx": "react-jsx", "strict": false, "moduleResolution": "node", "allowJs": true, "checkJs": false, "isolatedModules": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "lib": ["dom", "dom.iterable", "esnext"], "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "strictNullChecks": false}, "include": ["src"], "exclude": ["node_modules", "dist"]}